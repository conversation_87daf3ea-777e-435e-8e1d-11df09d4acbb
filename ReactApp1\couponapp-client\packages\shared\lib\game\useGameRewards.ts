import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useGameAtom } from '../atoms/atom-extensions'
import { useEffect } from 'react'
import { atomFamily, atomWithStorage } from 'jotai/utils'
import { useAtom } from 'jotai'
import { useGameRewardsDetails, useRewardSet } from './useGameRewardsDetails'

export interface CampaignReward {
    id: string
    image?: AssetUrl
    name?: string
    description?: string
}

export interface PickRewardResult {
    hasWon: boolean
    reward?: CampaignReward
}

export interface GameProgress {
    score?: number
    attempt?: number
    gameWidgetId: string
    cacheStrategy?: 'per-attempt'
}

export const useGameRewards = (gameProgress: GameProgress) => {
    const [reward, setReward] = useGameAtom<CampaignReward | null>(gameProgress.gameWidgetId, 'currentReward', null)
    const [hasWonReward, setHasWonReward] = useGameAtom<CampaignReward | null>(gameProgress.gameWidgetId, 'hasWonReward', null)

    const { rewards } = useRewardSet(gameProgress.gameWidgetId)

    const pickReward = async (): Promise<PickRewardResult> => {

        console.log("Game progress", gameProgress)

        if(gameProgress.cacheStrategy != null) { // moc
            await new Promise((resolve) => setTimeout(resolve, 600))
        }
        
        const mockedReward = rewards[0]
        const hasWon = gameProgress.attempt > 0 || gameProgress.score > 0
        setHasWonReward(hasWon)

        if(!hasWon) {
            return { hasWon: false }
        }

        setReward(mockedReward)
        return { hasWon, reward: mockedReward }
    }

    const pickRewardAtAttempt = async (attempt: number): Promise<PickRewardResult> => {
        if(gameProgress.cacheStrategy != null) { // mock that we have already cached the reard
            await new Promise((resolve) => setTimeout(resolve, 600))
        }

        const mockedReward = rewards[0]
        const hasWon = attempt > 0
        setHasWonReward(hasWon)

        if(!hasWon) {
            return { hasWon: false }
        }

        setReward(mockedReward)
        return { hasWon, reward: mockedReward }
    }

    return {
        reward,
        pickReward,
        hasWonReward,
        pickRewardAtAttempt
    }
}
